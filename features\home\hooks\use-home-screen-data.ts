import { useState, useEffect, useCallback } from 'react';
import { View } from 'react-native';
import { useTranslation } from 'react-i18next';
import {
  getStories,
  Story as ApiStory,
  GetStoriesOptions,
} from '@/api/stories';

export type StoryListTabType = 'recommended' | 'popular' | 'latest' | 'following';

export const useHomeScreenData = () => {
  const { t } = useTranslation();

  const storyListTabs = [
    t('homeScreen.tabs.recommended'),
    t('homeScreen.tabs.popular'),
    t('homeScreen.tabs.latest'),
    t('homeScreen.tabs.following'),
  ];

  const [activeStoryListTab, setActiveStoryListTab] = useState<string>(
    storyListTabs[0]
  );

  const [stories, setStories] = useState<ApiStory[]>([]);
  const [featuredStory, setFeaturedStory] = useState<ApiStory | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const storiesPerPage = 10;

  const mapTabToFilter = (tabLabel: string): GetStoriesOptions['filter'] => {
    if (tabLabel === t('homeScreen.tabs.popular')) return 'popular';
    if (tabLabel === t('homeScreen.tabs.latest')) return 'latest';
    if (tabLabel === t('homeScreen.tabs.following')) return 'latest';
    return 'latest';
  };

  const fetchStories = useCallback(
    async (filter: GetStoriesOptions['filter'], page: number = 0) => {
      setIsLoading(true);
      setError(null);
      try {
        const options: GetStoriesOptions = {
          filter: filter,
          limit: storiesPerPage,
          offset: page * storiesPerPage,
        };
        const { data: apiData, error: apiError } = await getStories(options);
        if (apiError) {
          throw new Error(
            apiError.message || t('errors.fetchStoriesFailed', '获取故事失败')
          );
        }
        const fetchedStories = apiData || [];
        setStories(fetchedStories);

        if (page === 0 && fetchedStories.length > 0) {
          if (filter === 'latest') {
            setFeaturedStory(fetchedStories[0]);
          } else if (!featuredStory && fetchedStories.length > 0) {
            setFeaturedStory(fetchedStories[0]);
          }
        } else if (page === 0 && fetchedStories.length === 0) {
          setFeaturedStory(null);
        }
      } catch (e: any) {
        console.error('Error fetching stories:', e);
        setError(e.message || t('errors.unknownError', '未知错误'));
        setStories([]);
        setFeaturedStory(null);
      } finally {
        setIsLoading(false);
      }
    },
    [t, featuredStory]
  );

  useEffect(() => {
    const currentFilter = mapTabToFilter(activeStoryListTab);
    fetchStories(currentFilter, 0);
    setCurrentPage(0);
  }, [activeStoryListTab, fetchStories]);

  return {
    storyListTabs,
    activeStoryListTab,
    setActiveStoryListTab,
    stories,
    featuredStory,
    isLoading,
    error,
    currentPage,
    fetchStories,
    mapTabToFilter,
  };
};
